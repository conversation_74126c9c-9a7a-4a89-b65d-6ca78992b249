/**
 * Token管理器
 * 
 * 统一管理Token的获取、验证、刷新和缓存
 * 解决Token验证时序问题和状态同步问题
 */

import {STORAGE_KEYS} from '@/constants'

interface TokenInfo {
  token: string
  expires: number
  refreshToken?: string
}

class TokenManager {
  private static instance: TokenManager
  private tokenCache: TokenInfo | null = null
  private refreshPromise: Promise<string> | null = null
  private isRefreshing = false
  
  // 防抖相关
  private logoutTimer: NodeJS.Timeout | null = null
  private hasLoggedOut = false

  private constructor() {
    // 初始化时从localStorage恢复token
    this.loadTokenFromStorage()
  }

  public static getInstance(): TokenManager {
    if (!TokenManager.instance) {
      TokenManager.instance = new TokenManager()
    }
    return TokenManager.instance
  }

  /**
   * 从localStorage加载token到缓存
   */
  private loadTokenFromStorage(): void {
    try {
      const token = localStorage.getItem(STORAGE_KEYS.TOKEN)
      const expiresStr = localStorage.getItem(STORAGE_KEYS.TOKEN_EXPIRES)
      
      if (token && expiresStr) {
        const expires = parseInt(expiresStr, 10)
        this.tokenCache = {
          token,
          expires,
          refreshToken: localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN) || undefined
        }
        console.log('🔑 Token已从localStorage加载到缓存')
      } else {
        this.tokenCache = null
        console.log('🔑 localStorage中没有有效的token')
      }
    } catch (error) {
      console.error('🔑 加载token失败:', error)
      this.tokenCache = null
    }
  }

  /**
   * 获取当前token
   * @param forceRefresh 是否强制从localStorage重新加载
   */
  public getToken(forceRefresh = false): string | null {
    if (forceRefresh || !this.tokenCache) {
      this.loadTokenFromStorage()
    }
    
    return this.tokenCache?.token || null
  }

  /**
   * 检查token是否有效（未过期）
   */
  public isTokenValid(): boolean {
    if (!this.tokenCache) {
      return false
    }

    // 检查是否过期（提前5分钟判断过期，留出刷新时间）
    const now = Date.now()
    const expiresAt = this.tokenCache.expires * 1000 // 转换为毫秒
    const bufferTime = 5 * 60 * 1000 // 5分钟缓冲时间
    
    const isValid = now < (expiresAt - bufferTime)
    
    if (!isValid) {
      console.log('🔑 Token即将过期或已过期:', {
        now: new Date(now).toLocaleString(),
        expires: new Date(expiresAt).toLocaleString(),
        isValid
      })
    }
    
    return isValid
  }

  /**
   * 设置token信息
   */
  public setToken(tokenInfo: TokenInfo): void {
    this.tokenCache = tokenInfo
    
    // 同步到localStorage
    localStorage.setItem(STORAGE_KEYS.TOKEN, tokenInfo.token)
    localStorage.setItem(STORAGE_KEYS.TOKEN_EXPIRES, tokenInfo.expires.toString())
    
    if (tokenInfo.refreshToken) {
      localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, tokenInfo.refreshToken)
    }
    
    console.log('🔑 Token已设置并同步到localStorage')
  }

  /**
   * 清除token
   */
  public clearToken(): void {
    this.tokenCache = null
    this.isRefreshing = false
    this.refreshPromise = null
    
    // 清除localStorage
    localStorage.removeItem(STORAGE_KEYS.TOKEN)
    localStorage.removeItem(STORAGE_KEYS.TOKEN_EXPIRES)
    localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN)
    
    console.log('🔑 Token已清除')
  }

  /**
   * 获取有效的token（如果即将过期则自动刷新）
   */
  public async getValidToken(): Promise<string | null> {
    const token = this.getToken()
    
    if (!token) {
      console.log('🔑 没有token')
      return null
    }

    if (this.isTokenValid()) {
      return token
    }

    // Token即将过期，尝试刷新
    console.log('🔑 Token即将过期，尝试刷新...')
    return await this.refreshToken()
  }

  /**
   * 刷新token
   */
  public async refreshToken(): Promise<string | null> {
    // 如果已经在刷新中，返回现有的Promise
    if (this.refreshPromise) {
      console.log('🔑 Token刷新已在进行中，等待结果...')
      return await this.refreshPromise
    }

    // 如果没有refreshToken，无法刷新
    if (!this.tokenCache?.refreshToken) {
      console.log('🔑 没有refreshToken，无法刷新')
      return null
    }

    this.isRefreshing = true
    this.refreshPromise = this.performTokenRefresh()

    try {
      return await this.refreshPromise
    } finally {
      this.isRefreshing = false
      this.refreshPromise = null
    }
  }

  /**
   * 执行token刷新
   */
  private async performTokenRefresh(): Promise<string | null> {
    try {
      // 这里应该调用刷新token的API
      // 暂时返回null，表示刷新失败
      console.log('🔑 Token刷新功能待实现')
      return null
    } catch (error) {
      console.error('🔑 Token刷新失败:', error)
      return null
    }
  }

  /**
   * 防抖登出处理
   */
  public handleTokenExpired(): void {
    if (this.hasLoggedOut) {
      console.log('🔑 已经处理过登出，忽略重复调用')
      return
    }

    // 清除之前的定时器
    if (this.logoutTimer) {
      clearTimeout(this.logoutTimer)
    }

    // 设置防抖定时器
    this.logoutTimer = setTimeout(() => {
      if (!this.hasLoggedOut) {
        this.hasLoggedOut = true
        this.performLogout()
      }
    }, 1000) // 1秒防抖
  }

  /**
   * 执行登出操作
   */
  private performLogout(): void {
    console.log('🔑 执行登出操作')
    
    // 清除token
    this.clearToken()
    
    // 保存当前页面路径
    const currentPath = window.location.pathname + window.location.search
    if (!currentPath.includes('/login')) {
      localStorage.setItem('redirectPath', currentPath)
    }

    // 跳转到登录页
    setTimeout(() => {
      if (window.location.pathname !== '/login') {
        window.location.href = '/login'
      }
    }, 500)
  }

  /**
   * 重置登出状态（用于重新登录后）
   */
  public resetLogoutState(): void {
    this.hasLoggedOut = false
    if (this.logoutTimer) {
      clearTimeout(this.logoutTimer)
      this.logoutTimer = null
    }
  }

  /**
   * 检查是否正在刷新token
   */
  public isRefreshingToken(): boolean {
    return this.isRefreshing
  }
}

export const tokenManager = TokenManager.getInstance()
